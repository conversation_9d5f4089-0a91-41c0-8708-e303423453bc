server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    # Tomcat启动初始化的线程数，默认值25
    accept-count: 50
    max-connections: 100
    threads:
      max: 80
      min-spare: 100
#日志配置
logging:
  level:
    com.sccl: DEBUG
    org.springframework: INFO
    org.spring.springboot.dao: INFO

#数据源配置
#数据源名称即datasource.names 这里的名称要与localnet.java里面的本地网编码一致 否则在切换时有问题
localds:
  names: ecm,rmp
#数据源基础配置
spring:
  datasource:
    druid:
      url:
      username:
      password:
      type: com.alibaba.druid.pool.DruidDataSource
      # 初始连接数
      initialSize: 10
      # 最大连接池数量
      maxActive: 100
      # 最小连接池数量
      minIdle: 10
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 100
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
  #redis配置
  redis:
    #单机环境
    host: ***********
    port: 30101
    username: nhpt
    password: 2bL*mnqVdxp+
    database: 0
    #客户端超时时间单位是毫秒 默认是2000
    timeout: 2000

#kafka (单独放在spring以外通过 @bean的方式注入)
kafka:
  bootstrap.servers: *************:9092,*************:9093,*************:9094
  #生产者
  producer:
    acks: all #server端将等待所有的副本都被接收后才发送确认。
    retries: 1 #生产者发送失败后，重试的次数
    batch.size: 16384 #当多条消息发送到同一个partition时，该值控制生产者批量发送消息的大小,提高客户端和服务端的性能
    linger.ms : 1 #设置为大于0的值，这样发送者将等待一段时间后，再向服务端发送请求，以实现每次请求可以尽可能多的发送批量消息。
    buffer.memory: 33554432 #生产者缓冲区的大小
    key.serializer: org.apache.kafka.common.serialization.StringSerializer
    value.serializer: org.apache.kafka.common.serialization.StringSerializer
  #消费者
  consumer:
    group.id: scnh-workflow
    enable.auto.commit: false
    auto.offset.reset: earliest
    auto.commit.interval.ms: 1000
    session.timeout.ms: 30000
    key.serializer: org.apache.kafka.common.serialization.StringDeserializer
    value.serializer: org.apache.kafka.common.serialization.StringDeserializer
  # Kerberos基础配合配置
  properties:
    security.protocol: SASL_PLAINTEXT
    java.security.krb5.realm: SC.COM
    java.security.krb5.kdc: scdcam1

datasource:
  config:
    ecm:
      url: *****************************************************************************************************************************************************
      username: ecm
      decrypt: false
      publickey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAK8QnBxPTDHWABPqpUABHnD0rX+dXScdEiIaRqdcWaSkHIePFrIjcZstZtbpMXdSphM00hb0YNqWNVrc1Kg2X80CAwEAAQ==
      password: 3e4r5t#$%
      driverClassName: com.p6spy.engine.spy.P6SpyDriver
    rmp:
      url: ************************************************************************************************************************
      username: rmp
      decrypt: false
      publickey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAK8QnBxPTDHWABPqpUABHnD0rX+dXScdEiIaRqdcWaSkHIePFrIjcZstZtbpMXdSphM00hb0YNqWNVrc1Kg2X80CAwEAAQ==
      password: 3e4r5t#$%
      driverClassName: com.mysql.jdbc.Driver
    lz:  #默认数据库
      url: ******************************************
      username: luzhou
      password: Bb2JHgR3mr253qSL5IV3VyAl/yhDfZN/zWCyEb3/lzHbVAMfw5Fo+afkF+EyeqU3omG3Zabzy649hQc31kJX4Q==
      #是否加密
      decrypt: true
      #加密后公钥
      publickey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJDsDcxn1ccMFpQ5I4tUDB80PmAODNhz2tb3qAP5BKqjCWsUOq8vtaiKqgu14zu2tn70Ez/yuWubjFzeVZ47T7cCAwEAAQ==

      #流程引擎PI地址
pi:
  url: http://10.206.23.226:9080/act-pi-webapp

#流程引擎PI地址
proc:
  inst:
    #url: http://10.206.20.188:8080/act-pi-webapp/procInstRestApi
    url: ${pi.url}/procInstRestApi

#是否同步请求流程引擎
is:
  sync:
    gen:
      task: true
    complete:
      task: false
#候选人角色
DO_CASE_TYPE_1_ROLECODE:  ROLE_491
DO_CASE_TYPE_2_ROLECODE:  ROLE_575
#辽宁推送OA待办webservice地址
oa:
  taskUrl: http://172.17.10.33:9081/UnifiedWorkbench/ProcessTaskService
  projectName: lnnh
  key: lnnh20190402

sccl:
  #文件上传路径
  profile: /data/bea/res/file/
  #部署环境
  deployTo: ln
  #单点登录时验证用
  appName: lnnh

# 辽宁 报账接口信息
MssInterface:
  MssClient:
    # 报账发送请求 taken
    PASSWORD: ZPIAPPL_LN:Zpiappl!1234
    # 报账发送请求 地址 http://136.127.56.5:9001/mssproxy
    SOAPURL: http://136.127.56.5:9001/mssproxy
  BaseInfo:
    # 报账发送xml 头部信息
    RETRY: 1
    S_PROVINCE: 26
    S_SYSTEM: LN-NH
    T_PROVINCE: 26
    T_SYSTEM: CW-CFBZ-BFSS
  MssJsonClient:
    # 报账电表信息、报账单信息 同步集团 open API 信息
    URL: http://10.206.20.36:8001
    NHURL: http://127.0.0.1:8888/api/openapi
    PROVINCECODE: 006
    APPKey: 3e97c481fc9225c133533ba709caa156
    AppSecret: a40fa3e3b0c317e550d5f04a74cab68c
    msgF: LNNH_
  HrId:
    # 查询 财辅 hrloginid 后缀 已存入数据库 无需再加
    orgEndStr: "@LN"#@SC
  #能耗接口编码
  serverName:
    #能耗集成报账接口模式一
    sn_bill: "NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_AutoCreateWriteoff"
    #能耗获取报账单状态
    sn_status: "NH_SI_CF_ESB_INTERGRATED_OUT_Syn_OP_GainWriteoffInstStatus"
    #能耗获取sap标准凭证信息
    sn_sap: "NH_SI_CF_Integrated_IN_Syn_OP_GetSapNumber"
    #能耗获取报账组织信息
    sn_org: "NH_SI_CF_Integrated_IN_Syn_OP_GetWriteoffBaseData"
  processCode: "FSJJSX_26"

# 四川单点登录 (防止注解报错)
OaClient:
  GET_ACCESS_TOKEN_URL: ""
  CLIEND_ID: ""
  RANGT_TYPE: ""
  PRIVATE_KEY: ""
  REDIRECT_URL: ""

# 四川 接口转发 doMss doWeb
scclTnterface:
  #  http://localhost:8080/energy-interface/httpforward/
  #  http://*************:8080/energy-interface/httpforward/
  httpforwardUrl: "http://localhost:8080/energy-interface/httpforward/"
towerMapUrl:  http://subsys.sctower.cn/ # http://*************:8081/ #铁塔地址subsys.sctower.cn 映射内网地址的URL
file:
  attachments:
    sc:
      endpoint: http://objs.paas.sc.ctc.com
      ak: BWDi1Kv0CwoxrwEfYfVV
      sk: aksJunzfPlXTXLCrxXlnPnGri69ofvUECYXdJBBA
      bucketName: scnh-file
Ai:
  ammeterCode:
    aiUrl: http://*************:8899/ai/aiFactoryServer/v1/apis/1/app-sqx-ammeteocr/ammeter_ocr
    accessKey: 484ee4a25c2c697cbda8eadb0e47083c
    accessSecret: 4a81751e87f37e63efc7705613631757
    percent: 0.7 # 认为识别成功时的概率阈值
    rightLen: 6 # 电表读数的正确长度
    minRightLen: 4 # 认为识别成功的读数最小长度
    openFormat: true # 是否开启格式化，开启后将会自动去除非数字字符并裁剪过长结果或补位缺失位数
    fillString: "*" # openforamt打开时，如果位数不够rightLen位将会填充该字符

permsCache:
  enable: true
  expireTime: 720
  cacheLength: 1000
  strategy: only_redis

#局站日均电量统计 离当前月份 偏离的最大月份
powerStationInfoAveDayElec:
    maxmonth: 12

decorator:
  datasource:
    p6spy:
      logging: slf4j

minio:
  access-key: Mjz0nPtegx7eUPE12snX
  secret-key: fa39wwL3e0H13TlOKUsu47UwWNpZemugT4cxls95
  end-point: http://136.96.228.144:9000
  bucket-name: ln-nh-oss
