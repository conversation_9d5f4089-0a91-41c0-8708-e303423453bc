package com.sccl.common.shiro.jwt.realm;

import com.sccl.common.cache.LocalCacheManager;
import com.sccl.common.shiro.jwt.token.JwtToken;
import com.sccl.common.utils.JwtUtils;
import com.sccl.common.utils.SpringUtil;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.modules.system.auth.config.PermsCacheConfig;
import com.sccl.modules.system.user.domain.User;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class UserRealm extends AuthorizingRealm {
    private static final Logger logger = LoggerFactory.getLogger(UserRealm.class);
 
    /*@Autowired
    private SysUserService sysUserService;*/


    /**
     * 必须重写此方法，不然Shiro会报错
     */
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof JwtToken;
    }

    /**
     * 只有当需要检测用户权限的时候才会调用此方法，例如checkRole,checkPermission之类的
     */
    @Override
    @SuppressWarnings("unchecked")
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        /*String username = JwtUtil.getUsername(principals.toString());
        SysUser user = sysUserService.findByUserName(username);*/
        User user = JwtUtils.getUserByToken(principals.toString());
        SimpleAuthorizationInfo authInfo = new SimpleAuthorizationInfo();
        if (user == null) {
            logger.error("ERROR,用户信息为null");
            return authInfo;
        }
        //authInfo.setRoles(roles);
        LocalCacheManager<String, Set<String>> cacheManager = (LocalCacheManager<String, Set<String>>) SpringUtil.getBean("permsCache", LocalCacheManager.class);
        PermsCacheConfig cacheConfig = SpringUtil.getBean(PermsCacheConfig.class);
        if (cacheConfig == null) {
            throw new NullPointerException("权限缓存配置为空");
        }
        String permsString = null;
        Set<String> perms = null;
        if (cacheConfig.isOpenPermsCache()) {
            //转为小写并去除空格
            cacheConfig.setStrategy(cacheConfig
                    .getStrategy()
                    .toLowerCase());
            cacheConfig.setStrategy(cacheConfig
                    .getStrategy()
                    .replace(" ", ""));
            switch (cacheConfig.getStrategy()) {
                case "default": {
                    if (cacheManager == null) {
                        logger.warn("default策略下本地缓存管理器为空，将从Redis尝试获取用户{}-{}的权限信息", user.getLoginId(), user.getUserName());
                        logger.info("用户：{}-{}权限过期时间：{}", user.getLoginId(), user.getUserName(), RedisUtil.getValidTime(user.getLoginId()));
                        perms = RedisUtil.get(user.getLoginId());
                        break;
                    }
                    perms = cacheManager.get(user.getLoginId());
                    if (perms == null) {
                        logger.warn("default策略下从本地缓存器获取权限为null，将尝试使用Redis获取");
                        logger.info("用户：{}-{}权限过期时间：{}", user.getLoginId(), user.getUserName(), RedisUtil.getValidTime(user.getLoginId()));
                        perms = RedisUtil.get(user.getLoginId());
                    }
                    break;
                }
                case "only_local": {
                    if (cacheManager == null) {
                        throw new NullPointerException("only_local策略下本地缓存管理器为空");
                    }
                    perms = cacheManager.get(user.getLoginId());
                    break;
                }
                case "only_redis": {
                    perms = RedisUtil.get(user.getLoginId());
                    logger.info("用户：{}-{}权限过期时间：{}", user.getLoginId(), user.getUserName(), RedisUtil.getValidTime(user.getLoginId()));
                    break;
                }
            }
        } else {
            logger.warn("权限填入失败，未开启权限缓存");
        }
        if (perms == null) {
            logger.error("在配置：过期时长：{}min，策略：{}下，用户{}-{}的权限信息为空", cacheConfig.getExpireTime(), cacheConfig.getStrategy(), user.getLoginId(), user.getUserName());
            return authInfo;
        }
        authInfo.setStringPermissions(perms);
        logger.info("调用鉴权方法，获取到的权限条数" + perms.size());
        return authInfo;
    }

    /**
     * 默认使用此方法进行用户名正确与否验证，错误抛出异常即可。
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken auth) throws AuthenticationException {
        String token = (String) auth.getCredentials();
        //首先验证的是 token是否过期
        String msg = JwtUtils.verifyToken(token);
        //logger.info("doGetAuthenticationInfo verifyToken " + msg);
        //这里如果 过期的话 要返回标识给前台 重新登录
        if (!"".equals(msg)) {
            throw new ExpiredCredentialsException(msg);
        }


        //SimpleAuthorizationInfo authInfo = new SimpleAuthorizationInfo();
        //authInfo.setRoles(roles);
        //authInfo.setStringPermissions(stringPermissions);


        return new SimpleAuthenticationInfo(token, token, "userRealm");
    }
}
