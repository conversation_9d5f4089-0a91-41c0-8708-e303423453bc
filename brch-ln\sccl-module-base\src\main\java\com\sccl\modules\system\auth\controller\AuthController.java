package com.sccl.modules.system.auth.controller;

import com.sccl.common.cache.LocalCacheManager;
import com.sccl.common.hlog.HLogUtils;
import com.sccl.common.io.PropertiesUtils;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.shiro.jwt.token.JwtToken;
import com.sccl.common.utils.*;
import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.utils.RedisUtil;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.domain.auth.LoginKey;
import com.sccl.modules.system.auth.config.AuthConstant;
import com.sccl.modules.system.auth.config.PermsCacheConfig;
import com.sccl.modules.system.auth.service.AuthService;
import com.sccl.modules.system.authority.service.IAuthorityService;
import com.sccl.modules.system.user.domain.ChangePassword;
import com.sccl.modules.system.user.domain.LoginParam;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.service.UserServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(value = "授权接口")
@RestController
@RequestMapping("/auth")
public class AuthController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    /*@Resource(name = "IdGenerator")
    private IdGenerator idGenerator;*/
    @Autowired
    private AuthService authService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private IAuthorityService menuService;

    /*=============================================>*/
    @Autowired(required = false)
    private LocalCacheManager<String, Set<String>> cacheManger;
    @Autowired
    private PermsCacheConfig config;

    @Value("${openLoginCache:false}")
    private Boolean isOpenLoginCache;
    /*=======================Finished======================<*/


    /**
     * 登录入口
     * 每次请求时都生成 一对密钥。将公钥放在前端。私钥放在session中。前端用公钥对密码加密。后端用私钥进行解密
     *
     * @param request 请求
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2019/1/10 21:26
     */
    @ApiOperation(value = "登录前获取公钥", notes = "每次登录前生成一对密钥，并返回私钥", httpMethod = "GET")

    @GetMapping("/publickey")
    public AjaxResult getPublicKey(HttpServletRequest request, @RequestParam("name") String name) {
        // 将公钥传到前端
        Map<String, String> map = new HashMap<String, String>();
        try {
            //生成公钥和私钥
            Map<String, Object> keyMap = RSAUtils.genKeyPair();
            RSAPublicKey publicKey = RSAUtils.getPublicKey(keyMap);
            RSAPrivateKey privateKey = RSAUtils.getPrivateKey(keyMap);
            //注意返回modulus和exponent以16为基数的BigInteger的字符串表示形式
            map.put("modulus", publicKey
                    .getModulus()
                    .toString(16));
            map.put("exponent", publicKey
                    .getPublicExponent()
                    .toString(16));
            //暂时把私钥放db里面 后期为了提高效率可能要放到redis里面
            //request.getSession().setAttribute(request.getSession().getId(), privateKey);
            authService.storeLoginKey(name, privateKey
                    .getModulus()
                    .toString(), privateKey
                    .getPrivateExponent()
                    .toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return this.success(map);
    }

    /**
     * 登录认证
     *
     * @param request 请求
     * @param param   包含 登录名 密码 验证码
     * @return com.sccl.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @Date 2019/2/18 09:42
     */
    @ApiOperation(value = "登录认证", notes = "登录认证", httpMethod = "POST")
    @ApiImplicitParam(name = "param", value = "登录所需实体LoginParam", required = true, dataType = "LoginParam")
    //@Log(title = "登录", action = BusinessType.LOGIN)

    @PostMapping(value = "/login", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public AjaxResult ajaxLogin(HttpServletRequest request, @RequestBody LoginParam param) {

        String passwordTxt = "";
        String username = param.getUsername();
        boolean isSSOSuccess = false;

        try {
            if (logger.isDebugEnabled()) {
                logger.debug("登录请求的 session-id: {}", request
                        .getSession()
                        .getId());
            }
            if (StringUtils.isBlank(param.getUsername())) {
                return AjaxResult.error(1, "用户名不能为空");
            }
            System.out.println("LoginParam:" + param);
            //System.out.println("LoginParam: LoginType:" + param.getLoginType());
            if ("0".equals(param.getLoginType())) {//用户名密码登录
                if (StringUtils.isBlank(param.getPassword())) {
                    return AjaxResult.error(1, "密码不能为空");
                }
                //将前台传入时替换的+号 换回来
                String password = param
                        .getPassword()
                        .replaceAll("%2B", "+");

                LoginKey loginKey = authService.getLoginPKbyName(username);
                RSAPrivateKey privateKey = RSAUtils.getPrivateKey(loginKey.getModulus(), loginKey.getExponent());
                if (privateKey == null) {
                    return AjaxResult.error(1, "登录失败，请联系管理员");
                }
                if (logger.isDebugEnabled()) {
                    logger.debug("私钥：{}", privateKey);
                }
                if (logger.isDebugEnabled()) {
                    logger.debug("前台传入的密码：{}", password);
                }
                //对密码进行解密
                passwordTxt = new String(RSAUtils.decryptByPrivateKey(password, (RSAPrivateKey) privateKey));
                if (logger.isDebugEnabled()) {
                    logger.debug("解密后的密码：{}", passwordTxt);
                }
            } else if ("1".equals(param.getLoginType())) {//单点登录
                LoginKey loginKey = authService.getLoginPKbyName(username);
                RSAPrivateKey privateKey = RSAUtils.getPrivateKey(loginKey.getModulus(), loginKey.getExponent());
                String encryptAppKey = param.getPassword();
                //对密码进行解密
                passwordTxt = new String(RSAUtils.decryptByPrivateKey(encryptAppKey, (RSAPrivateKey) privateKey));
                if (logger.isDebugEnabled()) logger.debug("receive : {}", passwordTxt);
                String appName = PropertiesUtils
                        .getInstance()
                        .getProperty("sccl.appName");
                String strDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
                String appKey = appName + strDate;

                if (logger.isDebugEnabled()) logger.debug("appkey : {}", appKey);
                String strMd5Key = MD5Utils.MD5Encode(appKey, "");
                if (logger.isDebugEnabled()) logger.debug("key md5 : {}", strMd5Key);
                logger.info("appkey : {}", appKey);
                //System.out.println("key md5 : " + strMd5Key + "receive:" + passwordTxt);
                if (strMd5Key.equals(passwordTxt)) {
                    isSSOSuccess = true;
                } else {
                    /*=============================================>*/
                    //写入登录日志
                    if (username != null) HLogUtils.writeLoginLog(username, "0");
                    /*=======================Finished======================<*/
                    throw new Exception("单点登录验证失败");
                }
            } else if ("ln".equals(param.getLoginType())) {// 辽宁单点登录
                String code = param.getUsername(); // 用 username 存放 code
                if (logger.isDebugEnabled()) logger.debug("sc receive : {}", code);
                logger.info("ln receive : {}", code);
                //System.out.println("sc receive : " + code + "length:" + code.length());
                username = authService.getUserByCodeLn(code);

                /*username = authService.getUserByOaCodeV2pc(code);*/
                System.out.println(" ln username : " + username);
//                username = "13388111083"; // 测试
                // oa 过来的是 usercode  存放到 remark 字段
                if (StringUtils.isNotEmpty(username)) {
                    isSSOSuccess = true;
                } else {
                    /*=============================================>*/
                    //写入登录日志
                    if (username != null) HLogUtils.writeLoginLog(username, "0");
                    /*=======================Finished======================<*/
                    throw new Exception("单点登录验证失败");
                }
            } else if ("scm".equals(param.getLoginType())) {
                String code = param.getUsername(); // 用 username 存放 code
                if (logger.isDebugEnabled()) logger.debug("scm receive : {}", code);
                logger.info("scm receive : {}", code);
                //System.out.println("scm receive : " + code + "length:" + code.length());
                username = authService.getUserByOaCodeV2(code);
                System.out.println("scm username : " + username);
                if (StringUtils.isNotEmpty(username)) {
                    isSSOSuccess = true;
                } else {
                    /*=============================================>*/
                    //写入登录日志
                    if (username != null) HLogUtils.writeLoginLog(username, "0");
                    /*=======================Finished======================<*/
                    throw new Exception("单点登录验证失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error(e.getMessage());
        }

        try {
            //这里做登录 数据库逻辑
            User user = null;
            if ("1".equals(param.getLoginType()) && isSSOSuccess) user = userService.selectUserByLoginId(username);
            else if ("ln".equals(param.getLoginType()) && isSSOSuccess) {
                // oa 过来的是 usercode  存放到 remark 字段
//                user = userService.selectUserByLoginIdSc(username);
                user = userService.selectUserByMobilePhone(username);
            } else if ("scm".equals(param.getLoginType()) && isSSOSuccess) {
                // oa 过来的是 usercode  存放到 remark 字段
//                user = userService.selectUserByLoginIdSc(username);
                user = userService.selectUserByLoginId(username);
            } else user = userService.selectByLoginNamePassword(username, passwordTxt);

            if (user == null) {
                /*=============================================>*/
                //写入登录日志
                if (username != null) HLogUtils.writeLoginLog(username, "0");
                /*=======================Finished======================<*/
                throw new Exception(MessageUtils.message("user.password.name.not.match", "user name or password not correct."));
            }
            System.out.println("Login: user:" + user);

            String alreadyLoginIP = RedisUtil.getStr(AuthConstant.LOGIN_IP_PREFIX + user.getLoginId());
            if (isOpenLoginCache != null && isOpenLoginCache && !StringUtils.isEmpty(alreadyLoginIP)) {
                if (!alreadyLoginIP.equalsIgnoreCase(ServletUtils.getRequestIp())) {
                    logger.warn("用户{}已在其他IP：{}登录，不可重复登录", user.getLoginId(), RedisUtil.getStr(AuthConstant.LOGIN_IP_PREFIX + user.getLoginId()));
                    return error("你已在其他终端登录，不可重复登录");
                }
            }

            /*=============================================>*/
            //当权限缓存开启时将User转为Map将不包含权限信息
            Map<String, String> map = JwtUtils.getMapFromUser(user, !config.isOpenPermsCache());
            //如果开启权限缓存，则将权限放入Redis和本地缓存
            if (config.isOpenPermsCache()) {
                //转为小写并去除空格
                config.setStrategy(config
                        .getStrategy()
                        .toLowerCase());
                config.setStrategy(config
                        .getStrategy()
                        .replace(" ", ""));
                switch (config.getStrategy()) {
                    case "default": {
                        RedisUtil.set(user.getLoginId(), user.getPerms());
                        RedisUtil.setValidTime(user.getLoginId(), config.getExpireTime() * 60 * 1000);
                        if (RedisUtil.hasKey(user.getLoginId())) {
                            logger.info("用户：{}-{}权限信息共{}条已存入Redis，过期时间：{}", user.getLoginId(), user.getUserName(), user
                                    .getPerms()
                                    .size(), RedisUtil.getValidTime(user.getLoginId()));
                        } else {
                            logger.error("警告！！！用户：{}-{}的权限信息插入Redis失败", user.getLoginId(), user.getUserName());
                        }
                        cacheManger.set(user.getLoginId(), user.getPerms());
                        break;
                    }
                    case "only_local": {
                        if (cacheManger != null) {
                            cacheManger.set(user.getLoginId(), user.getPerms());
                            logger.info("用户：{}-{}权限信息共{}条已存入本地缓存", user.getLoginId(), user.getUserName(), user.getPerms().size());
                        } else {
                            logger.error("警告！！！only_local策略下本地缓存管理器为空，用户：{}-{}的权限信息存储失败", user.getLoginId(), user.getUserName());
                        }
                        break;
                    }
                    case "only_redis": {
                        RedisUtil.set(user.getLoginId(), user.getPerms());
                        RedisUtil.setValidTime(user.getLoginId(), config.getExpireTime() * 60 * 1000);
                        if (RedisUtil.hasKey(user.getLoginId())) {
                            logger.info("用户：{}-{}权限信息共{}条已存入Redis，过期时间：{}", user.getLoginId(), user.getUserName(), user
                                    .getPerms()
                                    .size(), RedisUtil.getValidTime(user.getLoginId()));
                        } else {
                            logger.error("警告！！！用户：{}-{}的权限信息插入Redis失败", user.getLoginId(), user.getUserName());
                        }
                        break;
                    }
                }
            }
            /*=======================Finished======================<*/

            String jwt = JwtUtils.createToken(map);
            JwtToken token = new JwtToken(jwt);
            Subject subject = SecurityUtils.getSubject();
            subject.logout();
            request
                    .getSession()
                    .invalidate();//解决会话标识未更新漏洞
            subject.login(token);
            /*=============================================>*/
            //写入登录日志
            if (username != null) HLogUtils.writeLoginLog(username, "1");
            /*=======================Finished======================<*/

            //此处可以将用户信息与KEY放在缓存中，拦截器在拿到前端的token后验证是否已登录
            //Long id = idGenerator.getNextId();
            if (logger.isDebugEnabled()) {
                logger.debug("登录请求的 session-id: {}", request
                        .getSession()
                        .getId());
            }
            AjaxResult result = new AjaxResult();
            result.put("token", jwt);
            result.put("code", 0);
            result.put("msg", "操作成功");
            if (isOpenLoginCache != null && isOpenLoginCache) {
                String ip = ServletUtils.getRequestIp();
                if (!StringUtils.isEmpty(ip)) {
                    logger.info("当前登录用户IP：{}，已存入缓存", ip);
                    RedisUtil.set(AuthConstant.LOGIN_IP_PREFIX + user.getLoginId(), ServletUtils.getRequestIp());
                    RedisUtil.setValidTimeForString(AuthConstant.LOGIN_IP_PREFIX + user.getLoginId(), config.getExpireTime() * 60 * 1000);
                } else {
                    return error("服务器无法获取你的IP信息，为了系统安全，不予登录");
                }
            }
            return result;
        } catch (Exception e) {
            /*=============================================>*/
            //写入登录日志
            if (username != null) HLogUtils.writeLoginLog(username, "0");
            /*=======================Finished======================<*/
            e.printStackTrace();
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage())) {
                msg = e.getMessage();
            }
            return error(msg);
        }
    }

    /**
     * @description: 修改用户
     * @author: 芮永恒
     * @date: 2024-1-24 9:23
     * @param: * @param changePassword
     * @param request
     * @return: * @return com.sccl.framework.web.domain.AjaxResult
     **/
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "更改密码")
    @PostMapping(value = "changePassword")
    public AjaxResult changePassword(@RequestBody ChangePassword changePassword, HttpServletRequest request) throws Exception {
        // 获取当前登录用户
        String token = request.getHeader("Authorization");
        User currUser = JwtUtils.getUserByToken(token);
        if(currUser == null) return error("获取当前登录用户失败，请联系管理员");

        //将前台传入时替换的+号 换回来
        String oldPassword = changePassword.getOldPassword() .replaceAll("%2B", "+");

//        String oldPassword = decryptPassword(changePassword.getOldPassword());
//        logger.info("------------"+oldPassword);

        // 获取解密密钥
        LoginKey loginKey = authService.getLoginPKbyName(currUser.getLoginId());
        RSAPrivateKey privateKey = RSAUtils.getPrivateKey(loginKey.getModulus(), loginKey.getExponent());

        //对密码进行解密
        String oldPasswordTxt = RSAUtils.decryptByPrivateKey(oldPassword, privateKey);
        // 数据库比对用户名密码
        User user = userService.selectByLoginNamePassword(currUser.getLoginId(), oldPasswordTxt);
        if(user == null) return error("旧密码错误，请确认");

        //对新密码进行解密
        String newPassword = changePassword.getNewPassword() .replaceAll("%2B", "+");
        String newPasswordTxt = RSAUtils.decryptByPrivateKey(newPassword, privateKey);

        // 对确认的密码进行解密
        String confirmPassword = changePassword.getConfirmPassword() .replaceAll("%2B", "+");
        String confirmPasswordTxt = RSAUtils.decryptByPrivateKey(confirmPassword, privateKey);

        // 比对两次密码输入是否一致
        if(!confirmPasswordTxt.equals(newPasswordTxt)) return error("新密码两次输入不一样，请重新输入");
        // 比对新旧密码是否一致
//        if(confirmPassword.equals(oldPasswordTxt)) return error("新旧密码一致，请确认");

        // 存入数据库的新密码（加密后）
        String newPasswordDB = ResBase64Utils.encodePassword(newPasswordTxt);
        String pattern = "^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,16}$";
        boolean matches = Pattern.matches(pattern, newPasswordTxt);
        if(!matches) return error("该密码不符合规范");
//        if(true) return error("-----------验证正确，暂时阻断---------");
        // 实现数据库保存
        user.setPassword(newPasswordDB);
        user.setPwdUpdateTime(new Date());
        Integer integer = userService.changePassword(user);
        if(integer != 1) return error("密码修改失败，请联系管理员");
        return success();
    }

    @PostMapping(value = "/clear_login_cache", produces = "application/json;charset=UTF-8")
    public String clearLoginCache(@RequestBody(required = false) List<String> userLoginID) {
        if (userLoginID == null || userLoginID.size() == 0) {
            return MessageMaster.getMessage(MessageMaster.Code.BAD_REQUEST, "请指定参数");
        }
        Long count = RedisUtil
                .getStringRedisTemplate()
                .delete(userLoginID
                        .stream()
                        .map(item -> AuthConstant.LOGIN_IP_PREFIX + item)
                        .collect(Collectors.toList()));
        return MessageMaster.getMessage(MessageMaster.Code.OK, "成功清除" + count + "个用户的登录缓存");
    }


    /**
     * 登录成功后获取用户的角色/权限相关信息
     *
     * @return com.sccl.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @Date 2019/2/18 10:57
     */
    @ApiOperation(value = "根据菜单ID获取权限列表", notes = "根据菜单ID获取权限列表", httpMethod = "GET")
    @GetMapping("/access")
    @ResponseBody
    public AjaxResult getAccess(@RequestParam("menuId") String menuId) {
        // 根据用户id取出对应的菜单
        List<String> access = menuService.getAccessListByMenuId(menuId);

        return this.success(access);
    }
}
